import { Navigate, Outlet, useNavigate } from 'react-router-dom';
import { ReactNode, useEffect } from 'react';
import { isTokenValid } from './helpers';
import { useUserAuthStore } from './store/auth';

export const ProtectedRoute = ({ children }: { children: ReactNode }) => {
  const { clearAuthData } = useUserAuthStore();
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();
  const navigate = useNavigate();

  useEffect(() => {
    if (!token || (token && !isTokenValid(token))) {
      if (token) {
        clearAuthData();
        console.error('Session expired, please login again');
      }
      navigate('/login');
    }
  }, [token, navigate, clearAuthData]);

  if (!token || (token && !isTokenValid(token))) {
    return null;
  }

  return children;
};

export const AuthRoute = () => {
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();

  return token ? <Navigate to="/" replace /> : <Outlet />;
};
