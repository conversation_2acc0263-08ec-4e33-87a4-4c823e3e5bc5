import { useState, ChangeEvent, useEffect, useRef } from 'react';
import { Button } from '../../components/button/onboardingButton';
import {
  ArrowCircleRight2,
  Calendar,
  Clock,
  Gallery,
  Location,
} from 'iconsax-react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  fadeInRightTextVariants,
  fadeInRightLetterVariants,
} from '../../components/reuseables/animations/animations';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { format } from 'date-fns';
import { useMutation } from '@tanstack/react-query';
import { events, CreateEventPayload } from '../../lib/services/events';
import { toast } from 'react-toastify';

interface EventDetailsFormProps {
  onNext: (details: {
    name: string;
    startDate: string;
    startTime: string;
    endDate: string;
    endTime: string;
    location: string;
    image?: File;
    imageUrl?: string;
  }) => void;
  initialData?: {
    name: string;
    startDate?: string;
    startTime?: string;
    endDate?: string;
    endTime?: string;
    location: string;
    imageUrl?: string;
    imageFile?: File;
  };
  direction: 'forward' | 'backward';
}

export const EventDetailsForm = ({
  onNext,
  initialData,
  direction,
}: EventDetailsFormProps) => {
  const [eventTitle, setEventTitle] = useState(initialData?.name || '');

  // Start date state
  const [startDate, setStartDate] = useState<Date | undefined>(
    initialData?.startDate ? new Date(initialData.startDate) : undefined
  );
  const [startDateText, setStartDateText] = useState(
    initialData?.startDate || ''
  );
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);

  // End date state
  const [endDate, setEndDate] = useState<Date | undefined>(
    initialData?.endDate ? new Date(initialData.endDate) : undefined
  );
  const [endDateText, setEndDateText] = useState(initialData?.endDate || '');
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  // Time state
  const [startTime, setStartTime] = useState(() => {
    console.log('Setting initial startTime:', initialData?.startTime);
    return initialData?.startTime || '';
  });
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [endTime, setEndTime] = useState(() => {
    console.log('Setting initial endTime:', initialData?.endTime);
    return initialData?.endTime || '';
  });
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);

  // Location and image state
  const [eventLocation, setEventLocation] = useState(
    initialData?.location || ''
  );
  const [imageFile, setImageFile] = useState<File | null>(
    initialData?.imageFile || null
  );
  const [imagePreview, setImagePreview] = useState<string | null>(
    initialData?.imageUrl || null
  );

  // Refs for click outside handling
  const startDatePickerRef = useRef<HTMLDivElement>(null);
  const endDatePickerRef = useRef<HTMLDivElement>(null);
  const startTimePickerRef = useRef<HTMLDivElement>(null);
  const endTimePickerRef = useRef<HTMLDivElement>(null);

  const timeOptions = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = i % 2 === 0 ? '00' : '30';
    return `${hour.toString().padStart(2, '0')}:${minute}`;
  });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        startDatePickerRef.current &&
        !startDatePickerRef.current.contains(event.target as Node)
      ) {
        setShowStartDatePicker(false);
      }
      if (
        endDatePickerRef.current &&
        !endDatePickerRef.current.contains(event.target as Node)
      ) {
        setShowEndDatePicker(false);
      }
      if (
        startTimePickerRef.current &&
        !startTimePickerRef.current.contains(event.target as Node)
      ) {
        setShowStartTimePicker(false);
      }
      if (
        endTimePickerRef.current &&
        !endTimePickerRef.current.contains(event.target as Node)
      ) {
        setShowEndTimePicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    }
  };

  useEffect(() => {
    return () => {
      if (imagePreview && !initialData?.imageUrl) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview, initialData?.imageUrl]);

  const handleStartDateSelect = (date: Date | undefined) => {
    setStartDate(date);
    if (date) {
      setStartDateText(format(date, 'MMMM dd, yyyy'));
      setShowStartDatePicker(false);
      if (endDate && date > endDate) {
        setEndDate(undefined);
        setEndDateText('');
      }
    } else {
      setStartDateText('');
    }
  };

  const handleEndDateSelect = (date: Date | undefined) => {
    setEndDate(date);
    if (date) {
      setEndDateText(format(date, 'MMMM dd, yyyy'));
      setShowEndDatePicker(false);
    } else {
      setEndDateText('');
    }
  };

  const handleStartTimeSelect = (time: string) => {
    setStartTime(time);
    setShowStartTimePicker(false);
  };

  const handleEndTimeSelect = (time: string) => {
    setEndTime(time);
    setShowEndTimePicker(false);
  };

  const validateForm = () => {
    return (
      eventTitle.trim() !== '' &&
      startDateText.trim() !== '' &&
      startTime.trim() !== '' &&
      endDateText.trim() !== '' &&
      endTime.trim() !== '' &&
      eventLocation.trim() !== ''
    );
  };

  // Add React Query mutation
  const createEventMutation = useMutation({
    mutationFn: (eventData: CreateEventPayload) => events.createEvent(eventData),
    onSuccess: (data) => {
      console.log('Event created successfully:', data);
      toast.success('Event created successfully!');
      
      // Continue with the onboarding flow
      const eventDetails = {
        name: eventTitle,
        startDate: startDateText,
        startTime: startTime,
        endDate: endDateText,
        endTime: endTime,
        location: eventLocation,
        image: imageFile || undefined,
        imageUrl: imagePreview || undefined,
      };
      
      onNext(eventDetails);
    },
    onError: (error: any) => {
      console.error('Error creating event:', error);
      toast.error(error?.response?.data?.message || 'Failed to create event');
    }
  });

  const handleSubmit = () => {
    if (validateForm()) {
      // Format dates for API
      const formatDateTimeForAPI = (date: string, time: string) => {
        // Parse the date string (e.g., "January 15, 2023")
        const dateParts = new Date(date);
        
        // Parse the time string (e.g., "14:30")
        const [hours, minutes] = time.split(':').map(Number);
        
        // Create a new date with both date and time components
        dateParts.setHours(hours, minutes, 0, 0);
        
        // Format as ISO string for API
        return dateParts.toISOString();
      };
      
      // Create payload for API
      const eventPayload: CreateEventPayload = {
        category_id: '1', // You'll need to get this from previous step
        title: eventTitle,
        date_from: formatDateTimeForAPI(startDateText, startTime),
        date_to: formatDateTimeForAPI(endDateText, endTime),
        location_address: eventLocation
      };
      
      console.log('Creating event with payload:', eventPayload);
      
      // Call the mutation
      createEventMutation.mutate(eventPayload);
    } else {
      console.log('Form validation failed. Current values:', {
        eventTitle,
        startDate: startDateText,
        startTime,
        endDate: endDateText,
        endTime,
        location: eventLocation,
      });
    }
  };

  const getCardVariants = (direction: 'forward' | 'backward') => ({
    hidden: {
      x: direction === 'forward' ? '20vw' : '-20vw',
      rotate: direction === 'forward' ? 4 : -4,
      opacity: 0,
    },
    visible: {
      x: 0,
      rotate: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 180,
        damping: 20,
        mass: 0.2,
        delay: 0.2,
        velocity: 2,
      },
    },
  });

  const datePickerStyles = {
    button: { color: '#4D55F2' },
    caption: { color: '#4D55F2' },
    day_selected: { backgroundColor: '#4D55F2' },
    day_today: { color: '#4D55F2', fontWeight: 'bold' },
  };

  const disabledDays = { before: new Date() };

  return (
    <div>
      <motion.h2
        className="text-xl md:text-[40px] font-medium leading-[114.99999999999999%] mb-10"
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={fadeInRightTextVariants}>
        <AnimatePresence mode="wait">
          {["Let's", 'curate', 'your'].map((word, i) => (
            <motion.span
              key={i}
              variants={fadeInRightLetterVariants}
              style={{
                display: 'inline-block',
                marginRight: '8px',
                transformOrigin: 'left center',
                position: 'relative',
              }}
              className={
                word === 'curate'
                  ? 'bg-gradient-to-b from-[#343CD8] via-[#A6AAF9] to-[#A6AAF9] bg-clip-text text-transparent italic'
                  : ''
              }>
              {word}
              <motion.span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  opacity: 0,
                }}
                initial={{ opacity: 0, x: -4 }}
                animate={{ opacity: 0 }}
                exit={{ opacity: 0.2, x: 4 }}>
                {word}
              </motion.span>
            </motion.span>
          ))}
          <br />
          {['first', 'event', '🎉'].map((word, i) => (
            <motion.span
              key={i}
              variants={fadeInRightLetterVariants}
              style={{
                display: 'inline-block',
                marginRight: '8px',
                transformOrigin: 'left center',
                position: 'relative',
              }}>
              {word}
              <motion.span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  opacity: 0,
                }}
                initial={{ opacity: 0, x: -4 }}
                animate={{ opacity: 0 }}
                exit={{ opacity: 0.2, x: 4 }}>
                {word}
              </motion.span>
            </motion.span>
          ))}
        </AnimatePresence>
      </motion.h2>
      <motion.div
        className="bg-white rounded-[20px] mt-14 px-5 py-6 w-full"
        initial="hidden"
        animate="visible"
        variants={getCardVariants(direction)}>
        {/* Image circle */}
        <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-cus-pink-900 flex items-center justify-center cursor-pointer relative overflow-hidden">
          <input
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="absolute inset-0 opacity-0 cursor-pointer z-10"
          />
          {imagePreview ? (
            <img
              src={imagePreview}
              alt="Event"
              className="absolute inset-0 w-full h-full object-cover"
            />
          ) : (
            <Gallery variant="Bulk" size="62" color="#992600" />
          )}
        </div>

        <div className="space-y-4 -mt-10">
          {/* Event Title */}
          <div>
            <label className="block text-grey-500 text-sm font-medium mb-2">
              Event Title
            </label>
            <input
              type="text"
              placeholder="Give your event a title"
              value={eventTitle}
              onChange={(e) => setEventTitle(e.target.value)}
              className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
            />
          </div>

          {/* Event Start Date and Time */}
          <div>
            <label className="block text-grey-500 text-sm font-medium mb-2">
              Event Start
            </label>
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Select Start Date"
                    value={startDateText}
                    readOnly
                    onClick={() => setShowStartDatePicker(true)}
                    className="w-full py-2.5 px-3.5 pl-10 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                  />
                  <div
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                    onClick={() => setShowStartDatePicker(true)}>
                    <Calendar variant="Bulk" size="20" color="#292D32" />
                  </div>

                  {/* Start Date Picker Modal */}
                  {showStartDatePicker && (
                    <div
                      ref={startDatePickerRef}
                      className="absolute z-10 mt-2 bg-white rounded-lg shadow-lg p-4">
                      <style>
                        {`
                          .rdp-day_selected:not(.rdp-day_disabled):not(.rdp-day_outside) {
                            background-color: #4D55F2;
                          }
                          .rdp-day_today {
                            font-weight: bold;
                            color: #4D55F2;
                          }
                          .rdp-button:hover:not([disabled]):not(.rdp-day_selected) {
                            background-color: rgba(77, 85, 242, 0.1);
                          }
                          .rdp-caption_label {
                            color: #4D55F2;
                            font-weight: 600;
                          }
                        `}
                      </style>
                      <DayPicker
                        mode="single"
                        selected={startDate}
                        onSelect={handleStartDateSelect}
                        disabled={disabledDays}
                        styles={datePickerStyles}
                        fromMonth={new Date()}
                      />
                      <div className="flex justify-between mt-3">
                        <button
                          onClick={() => {
                            setStartDate(undefined);
                            setStartDateText('');
                          }}
                          className="text-sm text-primary-650 hover:underline">
                          Clear
                        </button>
                        <button
                          onClick={() => setShowStartDatePicker(false)}
                          className="text-sm bg-primary-650 text-white px-3 py-1 rounded-full">
                          Done
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="w-[118px]">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Start Time"
                    value={startTime}
                    readOnly
                    onClick={() => setShowStartTimePicker(true)}
                    className="w-full py-2.5 pl-10 placeholder:text-sm rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                  />
                  <div
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                    onClick={() => setShowStartTimePicker(true)}>
                    <Clock variant="Bulk" size="20" color="#292D32" />
                  </div>

                  {/* Start Time Picker Dropdown */}
                  {showStartTimePicker && (
                    <div
                      ref={startTimePickerRef}
                      className="absolute z-10 mt-2 bg-white rounded-lg shadow-lg p-2 right-0 w-[120px] max-h-[200px] overflow-y-auto"
                      style={{ scrollbarWidth: 'thin' }}>
                      <div className="flex flex-col">
                        {timeOptions.map((time) => (
                          <button
                            key={time}
                            onClick={() => handleStartTimeSelect(time)}
                            className={`text-left px-3 py-2 hover:bg-primary-250 rounded-md text-sm ${
                              startTime === time
                                ? 'bg-primary-250 text-primary-650 font-medium'
                                : 'text-grey-500'
                            }`}>
                            {time}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Event End Date and Time */}
          <div>
            <label className="block text-grey-500 text-sm font-medium mb-2">
              Event End
            </label>
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Select End Date"
                    value={endDateText}
                    readOnly
                    onClick={() => setShowEndDatePicker(true)}
                    className="w-full py-2.5 px-3.5 pl-10 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                  />
                  <div
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                    onClick={() => setShowEndDatePicker(true)}>
                    <Calendar variant="Bulk" size="20" color="#292D32" />
                  </div>

                  {/* End Date Picker Modal */}
                  {showEndDatePicker && (
                    <div
                      ref={endDatePickerRef}
                      className="absolute z-10 mt-2 bg-white rounded-lg shadow-lg p-4">
                      <style>
                        {`
                          .rdp-day_selected:not(.rdp-day_disabled):not(.rdp-day_outside) {
                            background-color: #4D55F2;
                          }
                          .rdp-day_today {
                            font-weight: bold;
                            color: #4D55F2;
                          }
                          .rdp-button:hover:not([disabled]):not(.rdp-day_selected) {
                            background-color: rgba(77, 85, 242, 0.1);
                          }
                          .rdp-caption_label {
                            color: #4D55F2;
                            font-weight: 600;
                          }
                        `}
                      </style>
                      <DayPicker
                        mode="single"
                        selected={endDate}
                        onSelect={handleEndDateSelect}
                        disabled={{
                          before: startDate || new Date(),
                        }}
                        styles={datePickerStyles}
                        fromMonth={startDate || new Date()}
                      />
                      <div className="flex justify-between mt-3">
                        <button
                          onClick={() => {
                            setEndDate(undefined);
                            setEndDateText('');
                          }}
                          className="text-sm text-primary-650 hover:underline">
                          Clear
                        </button>
                        <button
                          onClick={() => setShowEndDatePicker(false)}
                          className="text-sm bg-primary-650 text-white px-3 py-1 rounded-full">
                          Done
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="w-[118px]">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="End Time"
                    value={endTime}
                    readOnly
                    onClick={() => setShowEndTimePicker(true)}
                    className="w-full py-2.5 pl-10 placeholder:text-sm rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                  />
                  <div
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                    onClick={() => setShowEndTimePicker(true)}>
                    <Clock variant="Bulk" size="20" color="#292D32" />
                  </div>

                  {/* End Time Picker Dropdown */}
                  {showEndTimePicker && (
                    <div
                      ref={endTimePickerRef}
                      className="absolute z-10 mt-2 bg-white rounded-lg shadow-lg p-2 right-0 w-[120px] max-h-[200px] overflow-y-auto"
                      style={{ scrollbarWidth: 'thin' }}>
                      <div className="flex flex-col">
                        {timeOptions.map((time) => (
                          <button
                            key={time}
                            onClick={() => handleEndTimeSelect(time)}
                            className={`text-left px-3 py-2 hover:bg-primary-250 rounded-md text-sm ${
                              endTime === time
                                ? 'bg-primary-250 text-primary-650 font-medium'
                                : 'text-grey-500'
                            }`}>
                            {time}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Location */}
          <div>
            <label className="block text-grey-500 text-sm font-medium mb-2">
              Location
            </label>
            <div className="relative">
              <input
                type="text"
                placeholder="Type your location"
                value={eventLocation}
                onChange={(e) => setEventLocation(e.target.value)}
                className="w-full py-2.5 px-3.5 pl-10 rounded-full  border border-grey-200 placeholder:text-grey-300 outline-none text-base"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                <Location variant="Bulk" size="20" color="#292D32" />
              </div>
            </div>
          </div>

          <Button
            variant="primary"
            size="md"
            className={`text-white mt-14 ${
              validateForm() ? 'bg-primary-650' : 'bg-primary-650/35'
            }`}
            onClick={handleSubmit}
            disabled={!validateForm() || createEventMutation.isPending}
            iconRight={
              createEventMutation.isPending ? (
                <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full" />
              ) : (
                <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
              )
            }>
            {createEventMutation.isPending ? 'Creating...' : 'Continue'}
          </Button>
        </div>
      </motion.div>
    </div>
  );
};
